Archive member included to satisfy reference by file (symbol)

F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivsi3.o)
                              obj/main.o (__aeabi_uidiv)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_divsi3.o)
                              obj/main.o (__aeabi_idiv)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_dvmd_tls.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivsi3.o) (__aeabi_idiv0)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_fixunsdfdi.o)
                              obj/BSPPrint.o (__aeabi_d2ulz)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldf3.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_fixunsdfdi.o) (__aeabi_dmul)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_addsubdf3.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_fixunsdfdi.o) (__aeabi_dsub)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixunsdfsi.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_fixunsdfdi.o) (__aeabi_d2uiz)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memset.o)
                              obj/stream_buffer.o (memset)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o)
                              obj/tasks.o (sprintf)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-stpcpy.o)
                              obj/tasks.o (stpcpy)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strlen-stub.o)
                              obj/tasks.o (strlen)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o) (_svfprintf_r)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (_dtoa_r)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (_free_r)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-impure.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o) (_impure_ptr)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (_localeconv_r)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (_malloc_r)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memchr-stub.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (memchr)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mlock.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o) (__malloc_lock)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o) (_Balloc)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-s_frexp.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (frexp)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sbrkr.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o) (_sbrk_r)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strncpy.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (strncpy)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (__ssprint_r)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-callocr.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o) (_calloc_r)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o) (__global_locale)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mlock.o) (__retarget_lock_acquire_recursive)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mbtowc_r.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o) (__ascii_mbtowc)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memmove.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o) (memmove)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reallocr.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o) (_realloc_r)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sbrkr.o) (errno)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strcmp.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o) (strcmp)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-wctomb_r.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o) (__ascii_wctomb)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-ctype_.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o) (_ctype_)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldivdf3.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o) (__aeabi_ddiv)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_cmpdf2.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (__aeabi_dcmpeq)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_unorddf2.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (__aeabi_dcmpun)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixdfsi.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (__aeabi_d2iz)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_aeabi_uldivmod.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (__aeabi_uldivmod)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivmoddi4.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_aeabi_uldivmod.o) (__udivmoddi4)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzdi2.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivmoddi4.o) (__clzdi2)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzsi2.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzdi2.o) (__clzsi2)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a(sbrk.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sbrkr.o) (_sbrk)

Allocating common symbols
Common symbol       size              file

rdata               0x4               obj/gic_handle_irq.o
__lock___atexit_recursive_mutex
                    0x1               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
__lock___arc4random_mutex
                    0x1               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
StartTask_Handler   0x4               obj/main.o
errno               0x4               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
rlt                 0x1               obj/timer.o
fiq_num             0x4               obj/gic_handle_irq.o
xQueueRegistry      0x40              obj/queue.o
error_temp          0x4               obj/FreeRTOS_aux.o
__lock___env_recursive_mutex
                    0x1               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
__lock___sinit_recursive_mutex
                    0x1               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
irq_num             0x4               obj/gic_handle_irq.o
__lock___malloc_recursive_mutex
                    0x1               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
__lock___at_quick_exit_mutex
                    0x1               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
__lock___dd_hash_mutex
                    0x1               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
__lock___tz_mutex   0x1               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
rdata2              0x4               obj/gic_handle_irq.o
__lock___sfp_recursive_mutex
                    0x1               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
rlt_tmp             0x1               obj/apb_timer.o

Discarded input sections

 .data          0x00000000        0x0 obj/lowlevel.o
 .bss           0x00000000        0x0 obj/lowlevel.o
 .text          0x00000000       0x38 obj/mem_clear.o
 .data          0x00000000        0x0 obj/mem_clear.o
 .bss           0x00000000        0x0 obj/mem_clear.o
 .debug_line    0x00000000       0x54 obj/mem_clear.o
 .debug_info    0x00000000       0x26 obj/mem_clear.o
 .debug_abbrev  0x00000000       0x14 obj/mem_clear.o
 .debug_aranges
                0x00000000       0x20 obj/mem_clear.o
 .debug_str     0x00000000       0x4b obj/mem_clear.o
 .ARM.attributes
                0x00000000       0x25 obj/mem_clear.o
 .data          0x00000000        0x0 obj/start.o
 .bss           0x00000000        0x0 obj/start.o
 .text          0x00000000        0x0 obj/vectors.o
 .data          0x00000000        0x0 obj/vectors.o
 .bss           0x00000000        0x0 obj/vectors.o
 .debug_line    0x00000000       0x7a obj/vectors.o
 .debug_info    0x00000000       0x26 obj/vectors.o
 .debug_abbrev  0x00000000       0x14 obj/vectors.o
 .debug_aranges
                0x00000000       0x20 obj/vectors.o
 .debug_str     0x00000000       0x49 obj/vectors.o
 .ARM.attributes
                0x00000000       0x25 obj/vectors.o
 .data          0x00000000        0x0 obj/portASM.o
 .bss           0x00000000        0x0 obj/portASM.o
 .data          0x00000000        0x0 obj/FreeRTOS_aux.o
 COMMON         0x00000000        0x4 obj/FreeRTOS_aux.o
 .data          0x00000000        0x0 obj/main.o
 .bss           0x00000000        0x8 obj/main.o
 .rodata        0x00000000    0x1c028 obj/main.o
 .text          0x00000000       0x20 obj/apb_timer.o
 .data          0x00000000        0x0 obj/apb_timer.o
 .bss           0x00000000        0x0 obj/apb_timer.o
 .debug_info    0x00000000       0xf3 obj/apb_timer.o
 .debug_abbrev  0x00000000       0xb8 obj/apb_timer.o
 .debug_aranges
                0x00000000       0x20 obj/apb_timer.o
 .debug_line    0x00000000       0x8e obj/apb_timer.o
 .debug_str     0x00000000      0x1b0 obj/apb_timer.o
 .comment       0x00000000       0x7a obj/apb_timer.o
 .debug_frame   0x00000000       0x20 obj/apb_timer.o
 .ARM.attributes
                0x00000000       0x37 obj/apb_timer.o
 COMMON         0x00000000        0x1 obj/apb_timer.o
 .data          0x00000000        0x0 obj/BSPPrint.o
 .bss           0x00000000        0x0 obj/BSPPrint.o
 .data          0x00000000        0x0 obj/gic_handle_irq.o
 .bss           0x00000000        0x4 obj/gic_handle_irq.o
 .text          0x00000000      0x2e8 obj/gic_irq.o
 .data          0x00000000        0x0 obj/gic_irq.o
 .bss           0x00000000        0x0 obj/gic_irq.o
 .rodata.str1.4
                0x00000000       0x30 obj/gic_irq.o
 .debug_info    0x00000000      0x933 obj/gic_irq.o
 .debug_abbrev  0x00000000      0x274 obj/gic_irq.o
 .debug_loc     0x00000000      0x7de obj/gic_irq.o
 .debug_aranges
                0x00000000       0x20 obj/gic_irq.o
 .debug_ranges  0x00000000      0x2b8 obj/gic_irq.o
 .debug_line    0x00000000      0x421 obj/gic_irq.o
 .debug_str     0x00000000      0x34e obj/gic_irq.o
 .comment       0x00000000       0x7a obj/gic_irq.o
 .debug_frame   0x00000000      0x140 obj/gic_irq.o
 .ARM.attributes
                0x00000000       0x37 obj/gic_irq.o
 .data          0x00000000        0x0 obj/irq_gic.o
 .data          0x00000000        0x0 obj/mprintf.o
 .bss           0x00000000        0x0 obj/mprintf.o
 .data          0x00000000        0x0 obj/sram_on_chip_test.o
 .bss           0x00000000        0x0 obj/sram_on_chip_test.o
 .text          0x00000000      0x148 obj/timer.o
 .data          0x00000000        0x0 obj/timer.o
 .bss           0x00000000        0x0 obj/timer.o
 .debug_info    0x00000000      0x285 obj/timer.o
 .debug_abbrev  0x00000000      0x169 obj/timer.o
 .debug_aranges
                0x00000000       0x20 obj/timer.o
 .debug_line    0x00000000      0x152 obj/timer.o
 .debug_str     0x00000000      0x23c obj/timer.o
 .comment       0x00000000       0x7a obj/timer.o
 .debug_frame   0x00000000       0x50 obj/timer.o
 .ARM.attributes
                0x00000000       0x37 obj/timer.o
 COMMON         0x00000000        0x1 obj/timer.o
 .text          0x00000000       0xec obj/uart.o
 .data          0x00000000        0x0 obj/uart.o
 .bss           0x00000000        0x0 obj/uart.o
 .debug_info    0x00000000      0x29f obj/uart.o
 .debug_abbrev  0x00000000      0x1ad obj/uart.o
 .debug_loc     0x00000000      0x11d obj/uart.o
 .debug_aranges
                0x00000000       0x20 obj/uart.o
 .debug_ranges  0x00000000       0x18 obj/uart.o
 .debug_line    0x00000000      0x15a obj/uart.o
 .debug_str     0x00000000      0x1ff obj/uart.o
 .comment       0x00000000       0x7a obj/uart.o
 .debug_frame   0x00000000       0x70 obj/uart.o
 .ARM.attributes
                0x00000000       0x37 obj/uart.o
 .text          0x00000000        0x0 obj/croutine.o
 .data          0x00000000        0x0 obj/croutine.o
 .bss           0x00000000        0x0 obj/croutine.o
 .debug_info    0x00000000       0xd8 obj/croutine.o
 .debug_abbrev  0x00000000       0x9a obj/croutine.o
 .debug_aranges
                0x00000000       0x18 obj/croutine.o
 .debug_line    0x00000000       0x4a obj/croutine.o
 .debug_str     0x00000000      0x1c8 obj/croutine.o
 .comment       0x00000000       0x7a obj/croutine.o
 .ARM.attributes
                0x00000000       0x3d obj/croutine.o
 .text          0x00000000      0x46c obj/event_groups.o
 .data          0x00000000        0x0 obj/event_groups.o
 .bss           0x00000000        0x0 obj/event_groups.o
 .debug_info    0x00000000     0x172d obj/event_groups.o
 .debug_abbrev  0x00000000      0x3e8 obj/event_groups.o
 .debug_loc     0x00000000     0x106a obj/event_groups.o
 .debug_aranges
                0x00000000       0x20 obj/event_groups.o
 .debug_ranges  0x00000000       0x60 obj/event_groups.o
 .debug_line    0x00000000      0xb3a obj/event_groups.o
 .debug_str     0x00000000      0xb73 obj/event_groups.o
 .comment       0x00000000       0x7a obj/event_groups.o
 .debug_frame   0x00000000      0x14c obj/event_groups.o
 .ARM.attributes
                0x00000000       0x37 obj/event_groups.o
 .data          0x00000000        0x0 obj/list.o
 .bss           0x00000000        0x0 obj/list.o
 .data          0x00000000        0x0 obj/queue.o
 .bss           0x00000000        0x0 obj/queue.o
 .text          0x00000000      0xbb0 obj/stream_buffer.o
 .data          0x00000000        0x0 obj/stream_buffer.o
 .bss           0x00000000        0x0 obj/stream_buffer.o
 .debug_info    0x00000000     0x2693 obj/stream_buffer.o
 .debug_abbrev  0x00000000      0x446 obj/stream_buffer.o
 .debug_loc     0x00000000     0x282a obj/stream_buffer.o
 .debug_aranges
                0x00000000       0x20 obj/stream_buffer.o
 .debug_ranges  0x00000000       0xf8 obj/stream_buffer.o
 .debug_line    0x00000000     0x1782 obj/stream_buffer.o
 .debug_str     0x00000000      0xe14 obj/stream_buffer.o
 .comment       0x00000000       0x7a obj/stream_buffer.o
 .debug_frame   0x00000000      0x32c obj/stream_buffer.o
 .ARM.attributes
                0x00000000       0x37 obj/stream_buffer.o
 .data          0x00000000        0x0 obj/timers.o
 .data          0x00000000        0x0 obj/heap_4.o
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivsi3.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivsi3.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_divsi3.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_divsi3.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_dvmd_tls.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_dvmd_tls.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_fixunsdfdi.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_fixunsdfdi.o)
 .text          0x00000000      0x290 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldf3.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldf3.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldf3.o)
 .debug_frame   0x00000000       0x30 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldf3.o)
 .ARM.attributes
                0x00000000       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldf3.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_addsubdf3.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_addsubdf3.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixunsdfsi.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixunsdfsi.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memset.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memset.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memset.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o)
 .text._sprintf_r
                0x00000000       0x5c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-stpcpy.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-stpcpy.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-stpcpy.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strlen-stub.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strlen-stub.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strlen-stub.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-impure.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-impure.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-impure.o)
 .rodata._global_impure_ptr
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-impure.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o)
 .text.__localeconv_l
                0x00000000        0x8 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o)
 .text.localeconv
                0x00000000        0xc F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memchr-stub.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memchr-stub.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memchr-stub.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mlock.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mlock.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mlock.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text.__s2b    0x00000000       0xf0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text.__ulp    0x00000000       0x64 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text.__b2d    0x00000000       0xe0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text.__ratio  0x00000000       0x84 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text._mprec_log10
                0x00000000       0x5c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text.__copybits
                0x00000000       0x70 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text.__any_on
                0x00000000       0x64 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .rodata.__mprec_tinytens
                0x00000000       0x28 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-s_frexp.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-s_frexp.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-s_frexp.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sbrkr.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sbrkr.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sbrkr.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strncpy.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strncpy.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strncpy.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
 .rodata._svfiprintf_r.str1.4
                0x00000000       0x2f F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
 .text._svfiprintf_r
                0x00000000     0x1114 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
 .rodata.blanks.7324
                0x00000000       0x10 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
 .rodata.zeroes.7325
                0x00000000       0x10 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-callocr.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-callocr.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-callocr.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .text._setlocale_r
                0x00000000       0x68 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .text.__locale_mb_cur_max
                0x00000000       0x10 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .text.setlocale
                0x00000000       0x18 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .bss._PathLocale
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text.__retarget_lock_init
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text.__retarget_lock_init_recursive
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text.__retarget_lock_close
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text.__retarget_lock_close_recursive
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text.__retarget_lock_acquire
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text.__retarget_lock_try_acquire
                0x00000000        0x8 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text.__retarget_lock_try_acquire_recursive
                0x00000000        0x8 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text.__retarget_lock_release
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mbtowc_r.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mbtowc_r.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mbtowc_r.o)
 .text._mbtowc_r
                0x00000000       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mbtowc_r.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memmove.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memmove.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memmove.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reallocr.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reallocr.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reallocr.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
 .text.cleanup_glue
                0x00000000       0x2c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
 .text._reclaim_reent
                0x00000000       0xf0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
 .text          0x00000000      0x224 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strcmp.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strcmp.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strcmp.o)
 .debug_frame   0x00000000       0x40 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strcmp.o)
 .ARM.attributes
                0x00000000       0x1a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strcmp.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-wctomb_r.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-wctomb_r.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-wctomb_r.o)
 .text._wctomb_r
                0x00000000       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-wctomb_r.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-ctype_.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-ctype_.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-ctype_.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldivdf3.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldivdf3.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_cmpdf2.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_cmpdf2.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_unorddf2.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_unorddf2.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixdfsi.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixdfsi.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_aeabi_uldivmod.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_aeabi_uldivmod.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivmoddi4.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivmoddi4.o)
 .ARM.extab     0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivmoddi4.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzdi2.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzdi2.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzsi2.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzsi2.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a(sbrk.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a(sbrk.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a(sbrk.o)

Memory Configuration

Name             Origin             Length             Attributes
VECTORS          0x40000000         0x00001000         xr
FLASH            0x40001000         0x00017000         xr
RAM              0x40018000         0x000e8000         xrw
*default*        0x00000000         0xffffffff

Linker script and memory map

LOAD obj/lowlevel.o
LOAD obj/mem_clear.o
LOAD obj/start.o
LOAD obj/vectors.o
LOAD obj/portASM.o
LOAD obj/FreeRTOS_aux.o
LOAD obj/main.o
LOAD obj/apb_timer.o
LOAD obj/BSPPrint.o
LOAD obj/gic_handle_irq.o
LOAD obj/gic_irq.o
LOAD obj/irq_gic.o
LOAD obj/mprintf.o
LOAD obj/sram_on_chip_test.o
LOAD obj/timer.o
LOAD obj/uart.o
LOAD obj/croutine.o
LOAD obj/event_groups.o
LOAD obj/list.o
LOAD obj/queue.o
LOAD obj/stream_buffer.o
LOAD obj/tasks.o
LOAD obj/timers.o
LOAD obj/port.o
LOAD obj/heap_4.o
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libstdc++.a
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libm.a
START GROUP
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libg.a
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a
END GROUP
START GROUP
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a
END GROUP
                0x00002000                __stack_size__ = 0x2000
                0x00001000                __irq_stack_size__ = 0x1000
                0x00001000                __fiq_stack_size__ = 0x1000
                0x00010000                __heap_size__ = 0x10000

.vectors        0x40000000      0x244
                0x40000000                __vectors_start__ = .
 *(.vectors)
 .vectors       0x40000000      0x244 obj/vectors.o
                0x40000000                _start
                0x40000020                _undefined_instruction
                0x40000024                _software_interrupt
                0x40000028                _prefetch_abort
                0x4000002c                _data_abort
                0x40000030                _not_used
                0x40000034                _irq
                0x40000038                _fiq
                0x40000040                IRQ_STACK_START_IN
                0x40000044                FIQ_STACK_START_IN
                0x40000244                __vectors_end__ = .

.text           0x40001000     0xf488
                0x40001000                __text_start__ = .
 *(.text.reset)
 *(.text*)
 .text          0x40001000      0x2a0 obj/lowlevel.o
                0x40001000                lowlevel_init
                0x40001068                stack_test
                0x4000107c                stop_sim
                0x40001088                memcpy
                0x400010a4                uart_init
                0x400010a8                uart_out
                0x400010b4                puts
                0x400010d8                putnum
                0x400011b0                newline
                0x400011cc                readl
 .text          0x400012a0       0x9c obj/start.o
                0x400012a0                reset
                0x400012e0                c_runtime_cpu_setup
                0x400012f8                save_boot_params
                0x400012fc                cpu_init_cp15
                0x40001334                cpu_init_crit
 *fill*         0x4000133c        0x4 
 .text          0x40001340      0x2e0 obj/portASM.o
                0x40001340                FreeRTOS_SWI_Handler
                0x400013e8                vPortRestoreTaskContext
                0x40001450                FreeRTOS_IRQ_Handler
                0x400015c0                vApplicationIRQHandler
 .text          0x40001620       0xa0 obj/FreeRTOS_aux.o
                0x40001620                getSysInfo
                0x40001624                printTaskInfo
                0x40001670                vApplicationGetIdleTaskMemory
                0x40001698                vApplicationGetTimerTaskMemory
 .text          0x400016c0      0x45c obj/main.o
                0x400016c0                start_task
                0x400016e0                printDouble
                0x40001868                _write
                0x400018a0                MoniterRead
                0x400018b8                freertos_start
                0x400018f8                c_main
                0x40001964                apb_uart_init
                0x400019bc                outbyte
                0x400019e8                SYS_Delay
                0x40001a2c                get_tb_start
                0x40001a58                get_tb_end
                0x40001a68                get_tb_diff
                0x40001a70                delayus
                0x40001ac4                delay_ms
 .text          0x40001b1c      0x2d0 obj/BSPPrint.o
                0x40001bc8                print2
                0x40001dd4                pchar
 .text          0x40001dec      0x308 obj/gic_handle_irq.o
                0x40001dec                gic_handle_fiq
                0x40001fc0                apb_timer1_int_service
                0x4000202c                do_undefined_instruction
                0x40002054                do_software_interrupt
                0x40002090                do_prefetch_abort
                0x400020d0                do_data_abort
                0x400020e8                do_not_used
 .text          0x400020f4      0x388 obj/irq_gic.o
                0x400020f4                gic_get_irq_number
                0x40002108                gic_enable_irq
                0x4000212c                gic_disable_irq
                0x40002150                gic_eoi_irq
                0x40002160                gic_send_sgi
                0x40002178                gic_global_enable
                0x4000218c                gic_global_disable
                0x400021c0                gic_dist_config
                0x400022ac                gic_configure_irq
                0x40002320                gic_set_type
                0x40002338                gic_handle_irq_init
                0x40002344                gic_register_irq_entry
                0x40002358                gic_remove_irq_entry
                0x40002370                gic_handle_irq
                0x400023b0                gic_dist_init
                0x400023f8                gic_cpu_init
                0x40002414                gic_cpu_config
                0x40002418                disable_irq
                0x40002428                enable_irq
                0x40002438                gic_init
                0x40002474                SWI_Enable
 .text          0x4000247c      0x67c obj/mprintf.o
                0x400025f0                printnum
                0x40002624                print
                0x4000264c                isdigit_m
                0x40002660                tolower_m
                0x40002670                strlen_m
                0x40002698                mprintf
                0x40002a74                printreg
                0x40002ac0                printval
 .text          0x40002af8      0xac8 obj/sram_on_chip_test.o
                0x40002af8                sram_dancuo_irq_handler
                0x40002b4c                sram_duocuo_irq_handler
                0x40002bd4                sram_on_chip_regtest
                0x40002c64                sram_on_chip_rw_test
                0x40002d3c                sram_on_chip_bypass_test
                0x40002e40                sram_on_chip_dancuo_test
                0x400032f4                sram_on_chip_duocuo_test
 .text          0x400035c0       0xec obj/list.o
                0x400035c0                vListInitialise
                0x400035e4                vListInitialiseItem
                0x400035f0                vListInsertEnd
                0x4000361c                vListInsert
                0x40003674                uxListRemove
 .text          0x400036ac     0x1390 obj/queue.o
                0x400038dc                xQueueGenericReset
                0x400039a0                xQueueGenericCreateStatic
                0x40003a58                xQueueGenericCreate
                0x40003ad0                xQueueGetMutexHolder
                0x40003b04                xQueueGetMutexHolderFromISR
                0x40003b2c                xQueueCreateCountingSemaphoreStatic
                0x40003b84                xQueueCreateCountingSemaphore
                0x40003bc8                xQueueGenericSend
                0x40003da0                xQueueCreateMutexStatic
                0x40003df0                xQueueGiveMutexRecursive
                0x40003e50                xQueueCreateMutex
                0x40003e90                xQueueGenericSendFromISR
                0x40003fac                xQueueGiveFromISR
                0x400040a4                xQueueReceive
                0x40004248                xQueueSemaphoreTake
                0x40004458                xQueueTakeMutexRecursive
                0x400044b4                xQueuePeek
                0x40004654                xQueueReceiveFromISR
                0x40004738                xQueuePeekFromISR
                0x400047c0                uxQueueMessagesWaiting
                0x400047e8                uxQueueSpacesAvailable
                0x40004818                uxQueueMessagesWaitingFromISR
                0x40004834                vQueueDelete
                0x40004864                uxQueueGetQueueNumber
                0x4000486c                vQueueSetQueueNumber
                0x40004874                ucQueueGetQueueType
                0x4000487c                xQueueIsQueueEmptyFromISR
                0x400048a0                xQueueIsQueueFullFromISR
                0x400048cc                vQueueAddToRegistry
                0x40004968                pcQueueGetName
                0x400049b8                vQueueUnregisterQueue
                0x400049d0                vQueueWaitForMessageRestricted
 .text          0x40004a3c     0x2758 obj/tasks.o
                0x40005238                xTaskCreateStatic
                0x400052ec                xTaskCreate
                0x40005390                vTaskDelete
                0x400054a8                xTaskDelayUntil
                0x40005580                vTaskDelay
                0x400055e8                eTaskGetState
                0x40005684                uxTaskPriorityGet
                0x400056b0                uxTaskPriorityGetFromISR
                0x400056e0                vTaskPrioritySet
                0x40005824                vTaskSuspend
                0x40005950                vTaskResume
                0x40005a28                xTaskResumeFromISR
                0x40005b30                vTaskStartScheduler
                0x40005be0                vTaskEndScheduler
                0x40005c00                vTaskSuspendAll
                0x40005c18                xTaskResumeAll
                0x40005c3c                xTaskGetTickCount
                0x40005c4c                xTaskGetTickCountFromISR
                0x40005c64                uxTaskGetNumberOfTasks
                0x40005c74                pcTaskGetName
                0x40005ca4                xTaskCatchUpTicks
                0x40005d04                xTaskIncrementTick
                0x40005d30                vTaskSetApplicationTaskTag
                0x40005d58                xTaskGetApplicationTaskTag
                0x40005d80                xTaskGetApplicationTaskTagFromISR
                0x40005da8                xTaskCallApplicationTaskHook
                0x40005dd4                vTaskSwitchContext
                0x40005df8                vTaskPlaceOnEventList
                0x40005e34                vTaskPlaceOnUnorderedEventList
                0x40005ec8                vTaskPlaceOnEventListRestricted
                0x40005f44                xTaskRemoveFromEventList
                0x40006088                vTaskRemoveFromUnorderedEventList
                0x400061a0                vTaskSetTimeOutState
                0x400061d4                vTaskInternalSetTimeOutState
                0x400061ec                xTaskCheckForTimeOut
                0x4000629c                vTaskMissedYield
                0x400062b0                uxTaskGetTaskNumber
                0x400062bc                vTaskSetTaskNumber
                0x400062c8                vTaskSetThreadLocalStoragePointer
                0x40006308                pvTaskGetThreadLocalStoragePointer
                0x40006334                vTaskGetInfo
                0x400064a4                uxTaskGetSystemState
                0x400065d8                xTaskGetCurrentTaskHandle
                0x400065e8                xTaskGetSchedulerState
                0x40006618                xTaskPriorityInherit
                0x40006730                xTaskPriorityDisinherit
                0x4000682c                vTaskPriorityDisinheritAfterTimeout
                0x4000693c                vTaskList
                0x40006a48                uxTaskResetEventItemValue
                0x40006a70                pvTaskIncrementMutexHeldCount
                0x40006a98                ulTaskGenericNotifyTake
                0x40006b38                xTaskGenericNotifyWait
                0x40006c00                xTaskGenericNotify
                0x40006d98                xTaskGenericNotifyFromISR
                0x40006f98                vTaskGenericNotifyGiveFromISR
                0x40007104                xTaskGenericNotifyStateClear
                0x40007154                ulTaskGenericNotifyValueClear
 .text          0x40007194      0x7b8 obj/timers.o
                0x400075bc                xTimerCreateTimerTask
                0x40007648                xTimerCreate
                0x400076bc                xTimerCreateStatic
                0x4000775c                xTimerGenericCommand
                0x400077e4                xTimerGetTimerDaemonTaskHandle
                0x40007804                xTimerGetPeriod
                0x40007820                vTimerSetReloadMode
                0x40007858                xTimerGetReloadMode
                0x40007884                uxTimerGetReloadMode
                0x40007888                xTimerGetExpiryTime
                0x400078a4                pcTimerGetName
                0x400078c0                xTimerIsTimerActive
                0x400078ec                pvTimerGetTimerID
                0x40007914                vTimerSetTimerID
                0x4000793c                uxTimerGetTimerNumber
                0x40007944                vTimerSetTimerNumber
 .text          0x4000794c      0x660 obj/port.o
                0x400079e0                FreeRTOS_Tick_Handler
                0x40007a64                pxPortInitialiseStack
                0x40007b40                xPortStartScheduler
                0x40007cd4                vPortEndScheduler
                0x40007d28                vPortEnterCritical
                0x40007dd8                vPortExitCritical
                0x40007e38                vPortTaskUsesFPU
                0x40007e54                vPortClearInterruptMask
                0x40007e90                ulPortSetInterruptMask
                0x40007ed8                vPortValidateInterruptPriority
                0x40007f6c                vApplicationFPUSafeIRQHandler
 .text          0x40007fac      0x374 obj/heap_4.o
                0x40008028                pvPortMalloc
                0x400081a4                vPortFree
                0x40008218                xPortGetFreeHeapSize
                0x40008228                xPortGetMinimumEverFreeHeapSize
                0x40008238                vPortInitialiseBlocks
                0x4000823c                pvPortCalloc
                0x40008280                vPortGetHeapStats
 .text          0x40008320      0x114 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivsi3.o)
                0x40008320                __aeabi_uidiv
                0x40008320                __udivsi3
                0x40008414                __aeabi_uidivmod
 .text          0x40008434      0x148 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_divsi3.o)
                0x40008434                __divsi3
                0x40008434                __aeabi_idiv
                0x4000855c                __aeabi_idivmod
 .text          0x4000857c        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_dvmd_tls.o)
                0x4000857c                __aeabi_idiv0
                0x4000857c                __aeabi_ldiv0
 .text          0x40008580       0x5c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_fixunsdfdi.o)
                0x40008580                __fixunsdfdi
                0x40008580                __aeabi_d2ulz
 .text          0x400085dc      0x424 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_addsubdf3.o)
                0x400085dc                __aeabi_drsub
                0x400085e4                __aeabi_dsub
                0x400085e4                __subdf3
                0x400085e8                __aeabi_dadd
                0x400085e8                __adddf3
                0x400088f8                __floatunsidf
                0x400088f8                __aeabi_ui2d
                0x4000891c                __floatsidf
                0x4000891c                __aeabi_i2d
                0x40008944                __aeabi_f2d
                0x40008944                __extendsfdf2
                0x4000898c                __floatundidf
                0x4000898c                __aeabi_ul2d
                0x400089a0                __floatdidf
                0x400089a0                __aeabi_l2d
 .text          0x40008a00       0x54 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixunsdfsi.o)
                0x40008a00                __aeabi_d2uiz
                0x40008a00                __fixunsdfsi
 .text.memset   0x40008a54      0x11c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memset.o)
                0x40008a54                memset
 .text.sprintf  0x40008b70       0x6c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o)
                0x40008b70                sprintf
 .text.stpcpy   0x40008bdc       0x74 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-stpcpy.o)
                0x40008bdc                stpcpy
 .text.strlen   0x40008c50       0x60 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strlen-stub.o)
                0x40008c50                strlen
 .text._svfprintf_r
                0x40008cb0     0x27a0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
                0x40008cb0                _svfprintf_r
 .text.quorem   0x4000b450      0x1c0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o)
 .text._dtoa_r  0x4000b610     0x16d4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o)
                0x4000b610                _dtoa_r
 .text._malloc_trim_r
                0x4000cce4      0x100 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o)
                0x4000cce4                _malloc_trim_r
 .text._free_r  0x4000cde4      0x2ec F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o)
                0x4000cde4                _free_r
 .text._localeconv_r
                0x4000d0d0        0xc F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o)
                0x4000d0d0                _localeconv_r
 .text._malloc_r
                0x4000d0dc      0x7d4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
                0x4000d0dc                _malloc_r
 .text.memchr   0x4000d8b0       0xf8 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memchr-stub.o)
                0x4000d8b0                memchr
 .text.__malloc_lock
                0x4000d9a8       0x18 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mlock.o)
                0x4000d9a8                __malloc_lock
 .text.__malloc_unlock
                0x4000d9c0       0x18 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mlock.o)
                0x4000d9c0                __malloc_unlock
 .text._Balloc  0x4000d9d8       0x8c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x4000d9d8                _Balloc
 .text._Bfree   0x4000da64       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x4000da64                _Bfree
 .text.__multadd
                0x4000da80       0xd0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x4000da80                __multadd
 .text.__hi0bits
                0x4000db50       0x58 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x4000db50                __hi0bits
 .text.__lo0bits
                0x4000dba8       0xa0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x4000dba8                __lo0bits
 .text.__i2b    0x4000dc48       0x24 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x4000dc48                __i2b
 .text.__multiply
                0x4000dc6c      0x1f0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x4000dc6c                __multiply
 .text.__pow5mult
                0x4000de5c      0x104 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x4000de5c                __pow5mult
 .text.__lshift
                0x4000df60      0x118 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x4000df60                __lshift
 .text.__mcmp   0x4000e078       0x60 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x4000e078                __mcmp
 .text.__mdiff  0x4000e0d8      0x1e4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x4000e0d8                __mdiff
 .text.__d2b    0x4000e2bc      0x118 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x4000e2bc                __d2b
 .text.frexp    0x4000e3d4       0xa4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-s_frexp.o)
                0x4000e3d4                frexp
 .text._sbrk_r  0x4000e478       0x44 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sbrkr.o)
                0x4000e478                _sbrk_r
 .text.strncpy  0x4000e4bc       0xcc F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strncpy.o)
                0x4000e4bc                strncpy
 .text.__ssprint_r
                0x4000e588      0x1a4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
                0x4000e588                __ssprint_r
 .text._calloc_r
                0x4000e72c       0x9c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-callocr.o)
                0x4000e72c                _calloc_r
 .text.__retarget_lock_acquire_recursive
                0x4000e7c8        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
                0x4000e7c8                __retarget_lock_acquire_recursive
 .text.__retarget_lock_release_recursive
                0x4000e7cc        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
                0x4000e7cc                __retarget_lock_release_recursive
 .text.__ascii_mbtowc
                0x4000e7d0       0x44 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mbtowc_r.o)
                0x4000e7d0                __ascii_mbtowc
 .text.memmove  0x4000e814      0x158 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memmove.o)
                0x4000e814                memmove
 .text._realloc_r
                0x4000e96c      0x594 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reallocr.o)
                0x4000e96c                _realloc_r
 .text.__ascii_wctomb
                0x4000ef00       0x30 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-wctomb_r.o)
                0x4000ef00                __ascii_wctomb
 .text          0x4000ef30      0x49c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldivdf3.o)
                0x4000ef30                __aeabi_dmul
                0x4000ef30                __muldf3
                0x4000f1c0                __divdf3
                0x4000f1c0                __aeabi_ddiv
 .text          0x4000f3cc      0x144 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_cmpdf2.o)
                0x4000f3cc                __gtdf2
                0x4000f3cc                __gedf2
                0x4000f3d4                __ltdf2
                0x4000f3d4                __ledf2
                0x4000f3dc                __nedf2
                0x4000f3dc                __eqdf2
                0x4000f3dc                __cmpdf2
                0x4000f464                __aeabi_cdrcmple
                0x4000f480                __aeabi_cdcmpeq
                0x4000f480                __aeabi_cdcmple
                0x4000f498                __aeabi_dcmpeq
                0x4000f4b0                __aeabi_dcmplt
                0x4000f4c8                __aeabi_dcmple
                0x4000f4e0                __aeabi_dcmpge
                0x4000f4f8                __aeabi_dcmpgt
 .text          0x4000f510       0x38 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_unorddf2.o)
                0x4000f510                __unorddf2
                0x4000f510                __aeabi_dcmpun
 .text          0x4000f548       0x5c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixdfsi.o)
                0x4000f548                __aeabi_d2iz
                0x4000f548                __fixdfsi
 .text          0x4000f5a4       0x3c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_aeabi_uldivmod.o)
                0x4000f5a4                __aeabi_uldivmod
 .text          0x4000f5e0      0x130 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivmoddi4.o)
                0x4000f5e0                __udivmoddi4
 .text          0x4000f710       0x28 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzdi2.o)
                0x4000f710                __clzdi2
 .text          0x4000f738       0x48 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzsi2.o)
                0x4000f738                __clzsi2
 .text._sbrk    0x4000f780       0x2c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a(sbrk.o)
                0x4000f780                _sbrk
 *(.rodata*)
 .rodata.str1.4
                0x4000f7ac       0x47 obj/FreeRTOS_aux.o
 *fill*         0x4000f7f3        0x1 
 .rodata.str1.4
                0x4000f7f4       0x53 obj/main.o
 *fill*         0x4000f847        0x1 
 .rodata        0x4000f848       0x10 obj/BSPPrint.o
 .rodata.str1.4
                0x4000f858      0x32a obj/gic_handle_irq.o
 *fill*         0x4000fb82        0x2 
 .rodata.str1.4
                0x4000fb84       0x27 obj/irq_gic.o
 *fill*         0x4000fbab        0x1 
 .rodata.str1.4
                0x4000fbac       0x26 obj/mprintf.o
                                 0x2a (size before relaxing)
 *fill*         0x4000fbd2        0x2 
 .rodata.str1.4
                0x4000fbd4      0x5ff obj/sram_on_chip_test.o
 *fill*         0x400101d3        0x1 
 .rodata        0x400101d4       0x10 obj/sram_on_chip_test.o
 .rodata.str1.4
                0x400101e4       0x17 obj/tasks.o
 *fill*         0x400101fb        0x1 
 .rodata        0x400101fc        0x5 obj/tasks.o
 *fill*         0x40010201        0x3 
 .rodata.str1.4
                0x40010204       0x10 obj/timers.o
 .rodata        0x40010214       0x10 obj/port.o
                0x40010214                ulMaxAPIPriorityMask
                0x40010218                ulICCPMR
                0x4001021c                ulICCEOIR
                0x40010220                ulICCIAR
 .rodata._svfprintf_r.str1.4
                0x40010224       0x2b F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
                                 0x42 (size before relaxing)
 *fill*         0x4001024f        0x1 
 .rodata.blanks.7345
                0x40010250       0x10 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
 .rodata.zeroes.7346
                0x40010260       0x10 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
 .rodata._dtoa_r.str1.4
                0x40010270        0xd F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o)
                                 0x12 (size before relaxing)
 *fill*         0x4001027d        0x3 
 .rodata.__mprec_bigtens
                0x40010280       0x28 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x40010280                __mprec_bigtens
 .rodata.__mprec_tens
                0x400102a8       0xc8 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x400102a8                __mprec_tens
 .rodata.p05.6115
                0x40010370        0xc F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .rodata._setlocale_r.str1.4
                0x4001037c        0x6 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
                                  0xd (size before relaxing)
 .rodata.str1.4
                0x40010382        0x2 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 *fill*         0x40010382        0x2 
 .rodata._ctype_
                0x40010384      0x101 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-ctype_.o)
                0x40010384                _ctype_
 *(.glue_7)
 .glue_7        0x40010485        0x0 linker stubs
 *(.glue_7t)
 .glue_7t       0x40010485        0x0 linker stubs
 *(.eh_frame)
                0x40010488                . = ALIGN (0x4)
 *fill*         0x40010485        0x3 
                0x40010488                __preinit_array_start = .
 *(.preinit_array)
                0x40010488                __preinit_array_end = .
                0x40010488                . = ALIGN (0x4)
                0x40010488                __init_array_start = .
 *(SORT_BY_NAME(.init_array.*))
 *(.init_array)
                0x40010488                __init_array_end = .
                0x40010488                . = ALIGN (0x4)
                0x40010488                __fini_array_start = .
 *(.fini_array)
 *(SORT_BY_NAME(.fini_array.*))
                0x40010488                __fini_array_end = .
                0x40010488                __text_end__ = .

.vfp11_veneer   0x40010488        0x0
 .vfp11_veneer  0x40010488        0x0 linker stubs

.v4_bx          0x40010488        0x0
 .v4_bx         0x40010488        0x0 linker stubs

.iplt           0x40010488        0x0
 .iplt          0x40010488        0x0 obj/lowlevel.o

.rel.dyn        0x40010488        0x0
 .rel.iplt      0x40010488        0x0 obj/lowlevel.o

.ARM.exidx      0x40010488        0x8
 .ARM.exidx     0x40010488        0x8 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivmoddi4.o)
                0x40010490                . = ALIGN (0x4)
                0x40010490                _case_list_start = .

.test_case
 *(.test_case)
                0x40010490                _case_list_end = .

.data           0x40018000      0x9b4 load address 0x40010490
                0x40018000                __data_start__ = .
 *(.data*)
 .data          0x40018000        0x4 obj/tasks.o
                0x40018000                uxTopUsedPriority
 .data          0x40018004        0x4 obj/port.o
                0x40018004                ulCriticalNesting
 .data._impure_ptr
                0x40018008        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-impure.o)
                0x40018008                _impure_ptr
 *fill*         0x4001800c        0x4 
 .data.impure_data
                0x40018010      0x428 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-impure.o)
 .data.__malloc_av_
                0x40018438      0x408 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
                0x40018438                __malloc_av_
 .data.__malloc_sbrk_base
                0x40018840        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
                0x40018840                __malloc_sbrk_base
 .data.__malloc_trim_threshold
                0x40018844        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
                0x40018844                __malloc_trim_threshold
 .data.__global_locale
                0x40018848      0x16c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
                0x40018848                __global_locale
                0x400189b4                . = ALIGN (0x4)
                0x400189b4                __data_end__ = .
                0x40010490                __data_load__ = LOADADDR (.data)
                0x000009b4                __data_size__ = SIZEOF (.data)

.igot.plt       0x400189b4        0x0 load address 0x40010e44
 .igot.plt      0x400189b4        0x0 obj/lowlevel.o

.bss            0x400189b4     0xfe58 load address 0x40010e44
                0x400189b4                __bss_start__ = .
 *(.bss*)
 .bss           0x400189b4     0x30f8 obj/FreeRTOS_aux.o
 .bss           0x4001baac      0x280 obj/irq_gic.o
 .bss           0x4001bd2c      0x100 obj/tasks.o
                0x4001bd30                pxCurrentTCB
 .bss           0x4001be2c      0x104 obj/timers.o
 .bss           0x4001bf30        0xc obj/port.o
                0x4001bf30                ulPortInterruptNesting
                0x4001bf34                ulPortYieldRequired
                0x4001bf38                ulPortTaskHasFPUContext
 .bss           0x4001bf3c     0xc81c obj/heap_4.o
 .bss.__malloc_current_mallinfo
                0x40028758       0x28 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
                0x40028758                __malloc_current_mallinfo
 .bss.__malloc_max_sbrked_mem
                0x40028780        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
                0x40028780                __malloc_max_sbrked_mem
 .bss.__malloc_max_total_mem
                0x40028784        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
                0x40028784                __malloc_max_total_mem
 .bss.__malloc_top_pad
                0x40028788        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
                0x40028788                __malloc_top_pad
 .bss.heap_end.4144
                0x4002878c        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a(sbrk.o)
 *(COMMON)
 COMMON         0x40028790        0x4 obj/main.o
                0x40028790                StartTask_Handler
 COMMON         0x40028794       0x10 obj/gic_handle_irq.o
                0x40028794                rdata
                0x40028798                fiq_num
                0x4002879c                irq_num
                0x400287a0                rdata2
 COMMON         0x400287a4       0x40 obj/queue.o
                0x400287a4                xQueueRegistry
 COMMON         0x400287e4       0x21 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
                0x400287e4                __lock___atexit_recursive_mutex
                0x400287e8                __lock___arc4random_mutex
                0x400287ec                __lock___env_recursive_mutex
                0x400287f0                __lock___sinit_recursive_mutex
                0x400287f4                __lock___malloc_recursive_mutex
                0x400287f8                __lock___at_quick_exit_mutex
                0x400287fc                __lock___dd_hash_mutex
                0x40028800                __lock___tz_mutex
                0x40028804                __lock___sfp_recursive_mutex
 *fill*         0x40028805        0x3 
 COMMON         0x40028808        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
                0x40028808                errno
                0x4002880c                . = ALIGN (0x4)
                0x4002880c                __bss_end__ = .
                0x0000fe58                __bss_size__ = SIZEOF (.bss)

.heap           0x40028810    0x10000 load address 0x40010e48
                0x40028810                __heap_start__ = .
                0x40038810                . = (. + __heap_size__)
 *fill*         0x40028810    0x10000 
                0x40038810                __heap_end__ = .
                0x40038810                PROVIDE (end = .)
                [!provide]                PROVIDE (_end = .)
                [!provide]                PROVIDE (__end = .)
                0x40038810                __HeapLimit = __heap_end__

.stack          0x40038810     0x4000 load address 0x40010e48
                0x40038810                __stack_start__ = .
                0x40039810                . = (. + __fiq_stack_size__)
 *fill*         0x40038810     0x1000 
                0x40039810                __fiq_stack_top__ = .
                0x4003a810                . = (. + __irq_stack_size__)
 *fill*         0x40039810     0x1000 
                0x4003a810                __irq_stack_top__ = .
                0x4003c810                . = (. + __stack_size__)
 *fill*         0x4003a810     0x2000 
                0x4003c810                __stack_top__ = .
                0x4003c810                __stack_end__ = .

.ARM.attributes
                0x00000000       0x33
 *(.ARM.attributes)
 .ARM.attributes
                0x00000000       0x25 obj/lowlevel.o
 .ARM.attributes
                0x00000025       0x25 obj/start.o
 .ARM.attributes
                0x0000004a       0x27 obj/portASM.o
 .ARM.attributes
                0x00000071       0x37 obj/FreeRTOS_aux.o
 .ARM.attributes
                0x000000a8       0x37 obj/main.o
 .ARM.attributes
                0x000000df       0x37 obj/BSPPrint.o
 .ARM.attributes
                0x00000116       0x37 obj/gic_handle_irq.o
 .ARM.attributes
                0x0000014d       0x37 obj/irq_gic.o
 .ARM.attributes
                0x00000184       0x37 obj/mprintf.o
 .ARM.attributes
                0x000001bb       0x37 obj/sram_on_chip_test.o
 .ARM.attributes
                0x000001f2       0x37 obj/list.o
 .ARM.attributes
                0x00000229       0x37 obj/queue.o
 .ARM.attributes
                0x00000260       0x37 obj/tasks.o
 .ARM.attributes
                0x00000297       0x37 obj/timers.o
 .ARM.attributes
                0x000002ce       0x37 obj/port.o
 .ARM.attributes
                0x00000305       0x37 obj/heap_4.o
 .ARM.attributes
                0x0000033c       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivsi3.o)
 .ARM.attributes
                0x00000358       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_divsi3.o)
 .ARM.attributes
                0x00000374       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_dvmd_tls.o)
 .ARM.attributes
                0x00000390       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_fixunsdfdi.o)
 .ARM.attributes
                0x000003ba       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_addsubdf3.o)
 .ARM.attributes
                0x000003d6       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixunsdfsi.o)
 .ARM.attributes
                0x000003f2       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memset.o)
 .ARM.attributes
                0x0000041c       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o)
 .ARM.attributes
                0x00000446       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-stpcpy.o)
 .ARM.attributes
                0x00000470       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strlen-stub.o)
 .ARM.attributes
                0x0000049a       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
 .ARM.attributes
                0x000004c4       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o)
 .ARM.attributes
                0x000004ee       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o)
 .ARM.attributes
                0x00000518       0x30 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-impure.o)
 .ARM.attributes
                0x00000548       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o)
 .ARM.attributes
                0x00000572       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
 .ARM.attributes
                0x0000059c       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memchr-stub.o)
 .ARM.attributes
                0x000005c6       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mlock.o)
 .ARM.attributes
                0x000005f0       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .ARM.attributes
                0x0000061a       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-s_frexp.o)
 .ARM.attributes
                0x00000644       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sbrkr.o)
 .ARM.attributes
                0x0000066e       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strncpy.o)
 .ARM.attributes
                0x00000698       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
 .ARM.attributes
                0x000006c2       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-callocr.o)
 .ARM.attributes
                0x000006ec       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .ARM.attributes
                0x00000716       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .ARM.attributes
                0x00000740       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mbtowc_r.o)
 .ARM.attributes
                0x0000076a       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memmove.o)
 .ARM.attributes
                0x00000794       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reallocr.o)
 .ARM.attributes
                0x000007be       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
 .ARM.attributes
                0x000007e8       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-wctomb_r.o)
 .ARM.attributes
                0x00000812       0x30 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-ctype_.o)
 .ARM.attributes
                0x00000842       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldivdf3.o)
 .ARM.attributes
                0x0000085e       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_cmpdf2.o)
 .ARM.attributes
                0x0000087a       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_unorddf2.o)
 .ARM.attributes
                0x00000896       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixdfsi.o)
 .ARM.attributes
                0x000008b2       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_aeabi_uldivmod.o)
 .ARM.attributes
                0x000008ce       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivmoddi4.o)
 .ARM.attributes
                0x000008f8       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzdi2.o)
 .ARM.attributes
                0x00000914       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzsi2.o)
 .ARM.attributes
                0x00000930       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a(sbrk.o)

.comment        0x00000000       0x79
 *(.comment)
 .comment       0x00000000       0x79 obj/FreeRTOS_aux.o
                                 0x7a (size before relaxing)
 .comment       0x00000079       0x7a obj/main.o
 .comment       0x00000079       0x7a obj/BSPPrint.o
 .comment       0x00000079       0x7a obj/gic_handle_irq.o
 .comment       0x00000079       0x7a obj/irq_gic.o
 .comment       0x00000079       0x7a obj/mprintf.o
 .comment       0x00000079       0x7a obj/sram_on_chip_test.o
 .comment       0x00000079       0x7a obj/list.o
 .comment       0x00000079       0x7a obj/queue.o
 .comment       0x00000079       0x7a obj/tasks.o
 .comment       0x00000079       0x7a obj/timers.o
 .comment       0x00000079       0x7a obj/port.o
 .comment       0x00000079       0x7a obj/heap_4.o

/DISCARD/
 *(.note.GNU-stack)
 *(.gnu_debuglink)
 *(.gnu.lto_*)
                0x40018000                __ram_start__ = ORIGIN (RAM)
                0x40100000                __ram_end__ = (ORIGIN (RAM) + LENGTH (RAM))
                0x000e8000                __ram_size__ = LENGTH (RAM)
OUTPUT(soc2018_freertos.elf elf32-littlearm)

.debug_line     0x00000000     0xa157
 .debug_line    0x00000000       0xdd obj/lowlevel.o
 .debug_line    0x000000dd       0x76 obj/start.o
 .debug_line    0x00000153       0xaf obj/portASM.o
 .debug_line    0x00000202      0x31f obj/FreeRTOS_aux.o
 .debug_line    0x00000521      0x6ea obj/main.o
 .debug_line    0x00000c0b      0x416 obj/BSPPrint.o
 .debug_line    0x00001021      0x2f1 obj/gic_handle_irq.o
 .debug_line    0x00001312      0x4e6 obj/irq_gic.o
 .debug_line    0x000017f8      0x6c1 obj/mprintf.o
 .debug_line    0x00001eb9      0xb6e obj/sram_on_chip_test.o
 .debug_line    0x00002a27      0x3f6 obj/list.o
 .debug_line    0x00002e1d     0x22a5 obj/queue.o
 .debug_line    0x000050c2     0x31e1 obj/tasks.o
 .debug_line    0x000082a3      0xe29 obj/timers.o
 .debug_line    0x000090cc      0x919 obj/port.o
 .debug_line    0x000099e5      0x772 obj/heap_4.o

.debug_info     0x00000000    0x10f57
 .debug_info    0x00000000       0x26 obj/lowlevel.o
 .debug_info    0x00000026       0x26 obj/start.o
 .debug_info    0x0000004c       0x26 obj/portASM.o
 .debug_info    0x00000072      0xdad obj/FreeRTOS_aux.o
 .debug_info    0x00000e1f     0x108f obj/main.o
 .debug_info    0x00001eae      0x4f8 obj/BSPPrint.o
 .debug_info    0x000023a6      0x506 obj/gic_handle_irq.o
 .debug_info    0x000028ac      0x683 obj/irq_gic.o
 .debug_info    0x00002f2f     0x12b3 obj/mprintf.o
 .debug_info    0x000041e2      0xd01 obj/sram_on_chip_test.o
 .debug_info    0x00004ee3      0xbe4 obj/list.o
 .debug_info    0x00005ac7     0x3103 obj/queue.o
 .debug_info    0x00008bca     0x4355 obj/tasks.o
 .debug_info    0x0000cf1f     0x2214 obj/timers.o
 .debug_info    0x0000f133      0xeef obj/port.o
 .debug_info    0x00010022      0xf35 obj/heap_4.o

.debug_abbrev   0x00000000     0x2e51
 .debug_abbrev  0x00000000       0x14 obj/lowlevel.o
 .debug_abbrev  0x00000014       0x14 obj/start.o
 .debug_abbrev  0x00000028       0x14 obj/portASM.o
 .debug_abbrev  0x0000003c      0x2fa obj/FreeRTOS_aux.o
 .debug_abbrev  0x00000336      0x4b6 obj/main.o
 .debug_abbrev  0x000007ec      0x278 obj/BSPPrint.o
 .debug_abbrev  0x00000a64      0x18b obj/gic_handle_irq.o
 .debug_abbrev  0x00000bef      0x368 obj/irq_gic.o
 .debug_abbrev  0x00000f57      0x43f obj/mprintf.o
 .debug_abbrev  0x00001396      0x20f obj/sram_on_chip_test.o
 .debug_abbrev  0x000015a5      0x26b obj/list.o
 .debug_abbrev  0x00001810      0x4ae obj/queue.o
 .debug_abbrev  0x00001cbe      0x5a0 obj/tasks.o
 .debug_abbrev  0x0000225e      0x43d obj/timers.o
 .debug_abbrev  0x0000269b      0x3da obj/port.o
 .debug_abbrev  0x00002a75      0x3dc obj/heap_4.o

.debug_aranges  0x00000000      0x200
 .debug_aranges
                0x00000000       0x20 obj/lowlevel.o
 .debug_aranges
                0x00000020       0x20 obj/start.o
 .debug_aranges
                0x00000040       0x20 obj/portASM.o
 .debug_aranges
                0x00000060       0x20 obj/FreeRTOS_aux.o
 .debug_aranges
                0x00000080       0x20 obj/main.o
 .debug_aranges
                0x000000a0       0x20 obj/BSPPrint.o
 .debug_aranges
                0x000000c0       0x20 obj/gic_handle_irq.o
 .debug_aranges
                0x000000e0       0x20 obj/irq_gic.o
 .debug_aranges
                0x00000100       0x20 obj/mprintf.o
 .debug_aranges
                0x00000120       0x20 obj/sram_on_chip_test.o
 .debug_aranges
                0x00000140       0x20 obj/list.o
 .debug_aranges
                0x00000160       0x20 obj/queue.o
 .debug_aranges
                0x00000180       0x20 obj/tasks.o
 .debug_aranges
                0x000001a0       0x20 obj/timers.o
 .debug_aranges
                0x000001c0       0x20 obj/port.o
 .debug_aranges
                0x000001e0       0x20 obj/heap_4.o

.debug_str      0x00000000     0x328a
 .debug_str     0x00000000       0x4a obj/lowlevel.o
 .debug_str     0x0000004a       0x14 obj/start.o
                                 0x47 (size before relaxing)
 .debug_str     0x0000005e       0x28 obj/portASM.o
                                 0x5b (size before relaxing)
 .debug_str     0x00000086      0x789 obj/FreeRTOS_aux.o
                                0x831 (size before relaxing)
 .debug_str     0x0000080f      0x21f obj/main.o
                                0x80c (size before relaxing)
 .debug_str     0x00000a2e      0x209 obj/BSPPrint.o
                                0x360 (size before relaxing)
 .debug_str     0x00000c37      0x164 obj/gic_handle_irq.o
                                0x2e3 (size before relaxing)
 .debug_str     0x00000d9b      0x18c obj/irq_gic.o
                                0x373 (size before relaxing)
 .debug_str     0x00000f27      0x111 obj/mprintf.o
                                0x6f7 (size before relaxing)
 .debug_str     0x00001038       0xda obj/sram_on_chip_test.o
                                0x2cc (size before relaxing)
 .debug_str     0x00001112      0x121 obj/list.o
                                0x6fd (size before relaxing)
 .debug_str     0x00001233      0x9bc obj/queue.o
                               0x1115 (size before relaxing)
 .debug_str     0x00001bef      0xd3f obj/tasks.o
                               0x18cc (size before relaxing)
 .debug_str     0x0000292e      0x4f3 obj/timers.o
                                0xfb0 (size before relaxing)
 .debug_str     0x00002e21      0x1b2 obj/port.o
                                0x888 (size before relaxing)
 .debug_str     0x00002fd3      0x2b7 obj/heap_4.o
                                0x938 (size before relaxing)

.debug_loc      0x00000000     0x9b1b
 .debug_loc     0x00000000       0x5a obj/FreeRTOS_aux.o
 .debug_loc     0x0000005a      0x5d0 obj/main.o
 .debug_loc     0x0000062a      0x3d9 obj/BSPPrint.o
 .debug_loc     0x00000a03       0xba obj/gic_handle_irq.o
 .debug_loc     0x00000abd      0x472 obj/irq_gic.o
 .debug_loc     0x00000f2f      0xa8d obj/mprintf.o
 .debug_loc     0x000019bc      0x587 obj/sram_on_chip_test.o
 .debug_loc     0x00001f43       0x93 obj/list.o
 .debug_loc     0x00001fd6     0x2abc obj/queue.o
 .debug_loc     0x00004a92     0x33b6 obj/tasks.o
 .debug_loc     0x00007e48     0x1468 obj/timers.o
 .debug_loc     0x000092b0      0x2ee obj/port.o
 .debug_loc     0x0000959e      0x57d obj/heap_4.o

.debug_ranges   0x00000000      0xf28
 .debug_ranges  0x00000000       0x28 obj/FreeRTOS_aux.o
 .debug_ranges  0x00000028       0xa8 obj/main.o
 .debug_ranges  0x000000d0       0x88 obj/BSPPrint.o
 .debug_ranges  0x00000158       0x70 obj/irq_gic.o
 .debug_ranges  0x000001c8      0x120 obj/mprintf.o
 .debug_ranges  0x000002e8       0x48 obj/sram_on_chip_test.o
 .debug_ranges  0x00000330      0x2f8 obj/queue.o
 .debug_ranges  0x00000628      0x788 obj/tasks.o
 .debug_ranges  0x00000db0      0x140 obj/timers.o
 .debug_ranges  0x00000ef0       0x38 obj/port.o

.debug_frame    0x00000000     0x25f8
 .debug_frame   0x00000000       0x6c obj/FreeRTOS_aux.o
 .debug_frame   0x0000006c      0x164 obj/main.o
 .debug_frame   0x000001d0       0x94 obj/BSPPrint.o
 .debug_frame   0x00000264       0xd4 obj/gic_handle_irq.o
 .debug_frame   0x00000338      0x188 obj/irq_gic.o
 .debug_frame   0x000004c0      0x158 obj/mprintf.o
 .debug_frame   0x00000618      0x118 obj/sram_on_chip_test.o
 .debug_frame   0x00000730       0x68 obj/list.o
 .debug_frame   0x00000798      0x428 obj/queue.o
 .debug_frame   0x00000bc0      0x7b8 obj/tasks.o
 .debug_frame   0x00001378      0x29c obj/timers.o
 .debug_frame   0x00001614       0xf8 obj/port.o
 .debug_frame   0x0000170c       0xe4 obj/heap_4.o
 .debug_frame   0x000017f0       0x20 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivsi3.o)
 .debug_frame   0x00001810       0x20 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_divsi3.o)
 .debug_frame   0x00001830       0x34 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_fixunsdfdi.o)
 .debug_frame   0x00001864       0xac F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_addsubdf3.o)
 .debug_frame   0x00001910       0x24 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixunsdfsi.o)
 .debug_frame   0x00001934       0x50 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memset.o)
 .debug_frame   0x00001984       0x6c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o)
 .debug_frame   0x000019f0       0x2c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-stpcpy.o)
 .debug_frame   0x00001a1c       0x20 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strlen-stub.o)
 .debug_frame   0x00001a3c       0x50 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
 .debug_frame   0x00001a8c       0xc0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o)
 .debug_frame   0x00001b4c       0x74 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o)
 .debug_frame   0x00001bc0       0x40 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o)
 .debug_frame   0x00001c00       0x74 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
 .debug_frame   0x00001c74       0x4c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memchr-stub.o)
 .debug_frame   0x00001cc0       0x48 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mlock.o)
 .debug_frame   0x00001d08      0x2fc F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .debug_frame   0x00002004       0x34 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-s_frexp.o)
 .debug_frame   0x00002038       0x3c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sbrkr.o)
 .debug_frame   0x00002074       0x40 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strncpy.o)
 .debug_frame   0x000020b4       0xa0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
 .debug_frame   0x00002154       0x34 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-callocr.o)
 .debug_frame   0x00002188       0x50 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .debug_frame   0x000021d8       0xb0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .debug_frame   0x00002288       0x48 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mbtowc_r.o)
 .debug_frame   0x000022d0       0x34 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memmove.o)
 .debug_frame   0x00002304       0x70 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reallocr.o)
 .debug_frame   0x00002374       0x64 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
 .debug_frame   0x000023d8       0x3c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-wctomb_r.o)
 .debug_frame   0x00002414       0x50 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldivdf3.o)
 .debug_frame   0x00002464       0xc4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_cmpdf2.o)
 .debug_frame   0x00002528       0x20 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_unorddf2.o)
 .debug_frame   0x00002548       0x24 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixdfsi.o)
 .debug_frame   0x0000256c       0x2c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_aeabi_uldivmod.o)
 .debug_frame   0x00002598       0x40 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivmoddi4.o)
 .debug_frame   0x000025d8       0x20 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a(sbrk.o)
