
/**
 * Linker script for Cortex-A9 FreeRTOS project
 * Memory layout for SOC2018 platform
 */

/* Memory regions definition */
MEMORY
{
    /* 根据您的硬件平台调整这些地址 */
    VECTORS (rx)  : ORIGIN = 0x40000000, LENGTH = 0x1000     /* 4K for vectors */
    FLASH (rx)    : ORIGIN = 0x40001000, LENGTH = 0x17000    /* 92K for code */
    RAM (rwx)     : ORIGIN = 0x40018000, LENGTH = 0xE8000    /* 928K for data/bss/heap/stack */
}

/* Stack sizes */
__stack_size__ = 0x2000;        /* 8K main stack */
__irq_stack_size__ = 0x1000;    /* 4K IRQ stack */
__fiq_stack_size__ = 0x1000;    /* 4K FIQ stack */
__heap_size__ = 0x10000;        /* 64K heap for FreeRTOS */

/* Entry point */
ENTRY(_start)

SECTIONS
{
    /* Vector table must be at the beginning */
    .vectors : ALIGN(4)
    {
        __vectors_start__ = .;
        KEEP(*(.vectors))
        __vectors_end__ = .;
    } > VECTORS

    /* Code section */
    .text : ALIGN(4)
    {
        __text_start__ = .;
        *(.text.reset)
        *(.text*)
        *(.rodata*)
        *(.glue_7)
        *(.glue_7t)
        *(.eh_frame)

        /* C++ constructors/destructors */
        . = ALIGN(4);
        __preinit_array_start = .;
        KEEP (*(.preinit_array))
        __preinit_array_end = .;

        . = ALIGN(4);
        __init_array_start = .;
        KEEP (*(SORT(.init_array.*)))
        KEEP (*(.init_array))
        __init_array_end = .;

        . = ALIGN(4);
        __fini_array_start = .;
        KEEP (*(.fini_array))
        KEEP (*(SORT(.fini_array.*)))
        __fini_array_end = .;

        __text_end__ = .;
    } > FLASH

    /* Test case section */
    . = ALIGN(4);
    _case_list_start = .;
    .test_case :
    {
        *(.test_case)
    } > FLASH
    _case_list_end = .;

    /* Initialized data section */
    .data : ALIGN(4)
    {
        __data_start__ = .;
        *(.data*)
        . = ALIGN(4);
        __data_end__ = .;
    } > RAM AT > FLASH

    __data_load__ = LOADADDR(.data);
    __data_size__ = SIZEOF(.data);

    /* Uninitialized data section */
    .bss : ALIGN(4)
    {
        __bss_start__ = .;
        *(.bss*)
        *(COMMON)
        . = ALIGN(4);
        __bss_end__ = .;
    } > RAM

    __bss_size__ = SIZEOF(.bss);

    /* Heap section for FreeRTOS */
    .heap : ALIGN(8)
    {
        __heap_start__ = .;
        . = . + __heap_size__;
        __heap_end__ = .;
        PROVIDE(end = .);
        PROVIDE(_end = .);
        PROVIDE(__end = .);
        __HeapLimit = __heap_end__;
    } > RAM

    /* Stack section */
    .stack : ALIGN(8)
    {
        __stack_start__ = .;

        /* FIQ stack */
        . = . + __fiq_stack_size__;
        __fiq_stack_top__ = .;

        /* IRQ stack */
        . = . + __irq_stack_size__;
        __irq_stack_top__ = .;

        /* Main stack */
        . = . + __stack_size__;
        __stack_top__ = .;

        __stack_end__ = .;
    } > RAM

    /* Debug sections */
    .ARM.attributes 0 : { *(.ARM.attributes) }
    .comment 0 : { *(.comment) }

    /* Discard unwanted sections */
    /DISCARD/ :
    {
        *(.note.GNU-stack)
        *(.gnu_debuglink)
        *(.gnu.lto_*)
    }
}

/* Provide symbols for startup code */
__ram_start__ = ORIGIN(RAM);
__ram_end__ = ORIGIN(RAM) + LENGTH(RAM);
__ram_size__ = LENGTH(RAM);

