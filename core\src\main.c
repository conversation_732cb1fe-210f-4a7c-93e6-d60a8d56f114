#include "soc502_parameter.h"
#include "types.h"
#include "irq_gic.h"
#include "uart.h"
#include "math.h"
#include "stdio.h"
#include "pic.h"
#include "FreeRTOS.h"
#include "task.h"

#define OUT_TUBE
#define DEBUG
// #define QEMU_ENV

/* 初始化回退标志为 0 */
int global_flag = 0;

void outbyte(int byte);
void apb_uart_init(void);
void delayus(u32 us);
typedef unsigned char U8;
typedef unsigned int U32;
typedef  char I8;
typedef  int I32;

void printDouble(double v,int decDigits)
{
  int i = 1;
  int intPart, fractPart;
  int digits, digits0, bit0;
  int decimalDigits = decDigits+1;
  // print2("digits %d\n",decimalDigits);
  for (digits = decimalDigits;digits!=0; i*=10, digits--);
  intPart = (int)v;
  fractPart = (int)((v-(double)(int)v)*i);
  if(fractPart < 0) fractPart *= -1;

  if((fractPart%10)>4)
  {
    if (fractPart==(i-1))
    {
      if(v<0.0)
        intPart--;
      else
        intPart++;
      fractPart=0;
    }
    else
    {
      fractPart+=10;
    }
  }
  fractPart/=10;
  decimalDigits = decDigits;
  digits0 = 1;
  bit0 = decimalDigits;
  for (digits = decimalDigits;digits!=0; i*=10, digits--)
  {
    digits0 *= 10;
    bit0--;
    if((fractPart/digits0)==0) break;
  };

  if ((intPart==0)&&(v<0.0))
    print2("-0");
  else
    print2("%i", intPart);
  if(decDigits>0) print2(".");
  if (bit0>0)
  {
    for(i=0;i<bit0;i++) print2("0");
  }
  if(decDigits>0) print2("%i", fractPart);
}

int _write (int fd, char *pBuffer, int size)  
{  
    for (int i = 0; i < size; i++)  
    {  
        outbyte(pBuffer);
    }  
    return size;  
}

unsigned int MoniterRead(void)
{
   unsigned int tmp,tmp2;
    for (;;)
    {
        tmp = r32(APB_UART1_BASE + 0x08);
        if ( 0x00000004 == (tmp & 0x00000004 ))
        {
            tmp2 = r32(APB_UART1_BASE + 0x00);
	     	//print2("\r\nUARTSR:0x%x", tmp);
            return( tmp2 );	
        }
    }
}

static volatile uint32_t sink;

void start_task(void *pvParameters) {
    for (;;) {
        printf("Hello, this is 'start_task'!\n");
        vTaskDelay(pdMS_TO_TICKS(1000));
    }
}

//任务句柄
TaskHandle_t StartTask_Handler;

void freertos_start() {
    xTaskCreate((TaskFunction_t)start_task,
                (const char *)"start_task",
                (uint16_t)1024,
                (void *)NULL,
                (UBaseType_t)1,
                (TaskHandle_t *)&StartTask_Handler);
    vTaskStartScheduler();
}

void c_main(void) {
    /* 设备初始化 */
    apb_uart_init();
    gic_init();

    printf("Hello, this is 'main'!\n");

    freertos_start();
    
    while(1);
}

void apb_uart_init(void)
{
	w32(APB_UART1_BASE + 0x0c, 0x83);
	w32(APB_UART1_BASE + 0x00, 0x8B);//400MHz,38400
	w32(APB_UART1_BASE + 0x04, 0x2);
	w32(APB_UART1_BASE + 0x0c, 0x03);
	w32(APB_UART1_BASE + 0x04, 0x01); //2
	w32(APB_UART1_BASE + 0x08,0x07);

	w32(APB_UART0_BASE + 0x0c, 0x83);
	w32(APB_UART0_BASE + 0x00, 0x8B);//400MHz,38400
	w32(APB_UART0_BASE + 0x04, 0x2);
	w32(APB_UART0_BASE + 0x0c, 0x03);
	w32(APB_UART0_BASE + 0x04, 0x00); //2
	w32(APB_UART0_BASE + 0x08,0x07);
}

void outbyte(int byte)
{
//  while( !(r32(APB_UART0_BASE + 0x14) & 0x20) ) asm("nop");
//	w32(APB_UART0_BASE + 0x00, byte);
  while( !(r32(APB_UART1_BASE + 0x14) & 0x20) ) asm("nop");
	w32(APB_UART1_BASE + 0x00, byte);
}

void SYS_Delay(u32 dtm)
{
	u32 tmold;
	u32 tmnew;
	u32 tmdiff;
	u32 cmpval;

        w32(A9_PRIVATE_BASE + 0x08, 0x00000000);
        w32(A9_PRIVATE_BASE + 0x00, 0xffffffff);
        w32(A9_PRIVATE_BASE + 0x04, 0xffffffff);
        w32(A9_PRIVATE_BASE + 0x08, (249<<8 | 0x1<<1 |0x1));

	tmold  = r32(A9_PRIVATE_BASE + 0x04); 	

	cmpval = dtm*2;
	tmdiff = 0;
	while ( tmdiff < cmpval)
	{
		tmnew  = r32(A9_PRIVATE_BASE + 0x04); 	

		if ( tmnew <=tmold ) 
		{
			tmdiff = tmold -tmnew;
		}
		else  
		{
			tmdiff = 0xffffffff - tmnew + tmold +1;  
		}
	}
}

u32 get_tb_start(void)
{
	u32 tmval;
        w32(A9_PRIVATE_BASE + 0x08, 0x00000000);
        w32(A9_PRIVATE_BASE + 0x00, 0xffffffff);
        w32(A9_PRIVATE_BASE + 0x04, 0xffffffff);
        w32(A9_PRIVATE_BASE + 0x08, (249<<8 | 0x1<<1 |0x1));//500MHz / 250 = 2MHz

	tmval  = r32(A9_PRIVATE_BASE + 0x04); 	
	return tmval;
}

u32 get_tb_end(void)
{
	u32 tmval;
	tmval = r32(A9_PRIVATE_BASE + 0x04);
	return tmval;
}
u32 get_tb_diff(u32 tmold, u32 tmnew)
{
	u32 tmdiff;
	
	if ( tmnew <=tmold ) 
	{
		tmdiff = tmold -tmnew;
	}
	else  
	{
		tmdiff = 0xffffffff - tmnew + tmold +1;  
	}

	return tmdiff;
}
//
// delay function use global timer
//
#define TIMER_CLK       (500000000 / 1)
#define CLK_PER_US      (TIMER_CLK / 1000000) //--right

void delayus(u32 us)
{
        u32 timer_end;

        timer_end = us * CLK_PER_US;
        // reset timer values
        w32(A9_GLOBAL_TIMER_BASE + 0x08, 0x00000000);
        w32(A9_GLOBAL_TIMER_BASE + 0x00, 0x00000000);
        w32(A9_GLOBAL_TIMER_BASE + 0x04, 0x00000000);
        w32(A9_GLOBAL_TIMER_BASE + 0x10, 0x00000000);
        w32(A9_GLOBAL_TIMER_BASE + 0x14, 0x00000000);

        // clear status bit
        w32(A9_GLOBAL_TIMER_BASE + 0x0c, 0x00000001);

        // set the end value
        w32(A9_GLOBAL_TIMER_BASE + 0x10, timer_end);
        // start counter: single shot
        w32(A9_GLOBAL_TIMER_BASE + 0x08, 0x00000003);

        // waiting for counter reaching the end
        while(1) {
                if(r32(A9_GLOBAL_TIMER_BASE + 0x0c))
                        break;
        }
}

void delay_ms(u32 ms)
{
  delayus(ms*1000);
}
